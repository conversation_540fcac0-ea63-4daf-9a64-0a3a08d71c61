
// Required API endpoints:
// - /api/devices
// - /api/sales
// - /api/returns
// - /api/supply
// - /api/suppliers
// - /api/evaluations
// - /api/maintenance-orders
// - /api/warehouse-transfers
'use client';

import { useState, useEffect, useMemo } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
// TODO: Update to use API instead of store
import { useStore } from '@/context/store';
import { ar } from 'date-fns/locale';
import './track.css';
import './enhanced-styles.css';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  Barcode,
  Package,
  Wrench,
  ShoppingCart,
  Undo2,
  ClipboardCheck,
  Shuffle,
  User,
  PackageCheck,
  ShieldCheck,
  FileText,
  Replace,
  Printer,
  FileDown,
} from 'lucide-react';
import {
  addDays,
  addMonths,
  addYears,
  isAfter,
  formatDistanceToNowStrict,
  format,
} from 'date-fns';
import { exportDataToPDF, exportHTMLToPDF, exportDeviceTrackingReport, exportDeviceTrackingReportDirect, printElement, printDeviceData } from '@/lib/export-utils/html-to-pdf';
import { createArabicPDFWithCanvas } from '@/lib/export-utils/canvas-pdf';
import { printDeviceTrackingReport, printElementWithSettings } from '@/lib/device-tracking-utils';
import ReportPreview from '@/components/ReportPreview';
import './print-styles.css';

// دالة مساعدة لتنسيق التاريخ بالعربية
function formatArabicDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('ar-EG', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

type TimelineEvent = {
  icon: React.ReactNode;
  title: string;
  description: string;
  date: string; // ISO string for sorting
  color: string;
  user?: string;
  formattedDate?: string;
};

export default function TrackPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
    // State management
  const [isLoading, setIsLoading] = useState(true);
  const [devices, setDevices] = useState([]);
  const [sales, setSales] = useState([]);
  const [returns, setReturns] = useState([]);
  const [supplyOrders, setSupplyOrders] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [evaluationOrders, setEvaluationOrders] = useState([]);
  const [maintenanceHistory, setMaintenanceHistory] = useState([]);
  const [warehouseTransfers, setWarehouseTransfers] = useState([]);

  // TODO: Add API functions here

  const [imei, setImei] = useState('');
  const [searchedImei, setSearchedImei] = useState('');
  const [isCustomerView, setIsCustomerView] = useState(false);
  const [showReportPreview, setShowReportPreview] = useState(false);

  useEffect(() => {
    const idFromQuery = searchParams.get('id');
    if (idFromQuery) {
      setImei(idFromQuery);
      setSearchedImei(idFromQuery);
    }
  }, [searchParams]);

  const handleSearch = () => {
    if (!imei) return;
    setSearchedImei(imei);
    const params = new URLSearchParams(searchParams);
    params.set('id', imei);
    router.replace(`/track?${params.toString()}`);
  };

  const fullTimelineEvents = useMemo((): TimelineEvent[] => {
    if (!searchedImei) return [];

    const events: TimelineEvent[] = [];
    const device = devices.find((d) => d.id === searchedImei);

    // 1. Supply Event
    const supplyOrder = supplyOrders.find((so) =>
      (Array.isArray(so.items) && so.items.some((item) => item.imei === searchedImei))
    );
    if (supplyOrder) {
      const supplier = suppliers.find((s) => s.id === supplyOrder.supplierId);
      events.push({
        icon: <Package className="h-5 w-5" />,
        title: 'توريد',
        description: `تم استلام الجهاز من المورد '${supplier?.name || 'غير معروف'}' ضمن أمر التوريد ${supplyOrder.supplyOrderId}.`,
        date: supplyOrder.supplyDate,
        color: 'bg-cyan-500/20 text-cyan-400',
        user: supplyOrder.employeeName,
      });
    }

    // 2. Evaluation Events
    evaluationOrders.forEach((order) => {
      const evaluatedItem = (Array.isArray(order.items) ? order.items.find(
        (item) => item.deviceId === searchedImei
      ) : null);
      if (evaluatedItem) {
        events.push({
          icon: <ClipboardCheck className="h-5 w-5" />,
          title: 'فحص وتقييم',
          description: `تم فحص الجهاز. النتيجة: ${evaluatedItem.finalGrade}. ${evaluatedItem.fault || evaluatedItem.damageType || ''}`,
          date: order.date,
          color: 'bg-indigo-500/20 text-indigo-400',
          user: order.employeeName,
        });
      }
    });

    // 3. Maintenance Events
    maintenanceHistory.forEach((log) => {
      if (log.deviceId === searchedImei) {
        events.push({
          icon: <Wrench className="h-5 w-5" />,
          title: 'إتمام الصيانة',
          description: `تمت معالجة الجهاز في الصيانة. النتيجة: ${log.result}. ملاحظات: ${log.notes || 'لا يوجد'}.`,
          date: log.repairDate,
          color: 'bg-yellow-500/20 text-yellow-400',
          user: 'قسم الصيانة', // Assuming a generic user for now
        });
        if (log.status === 'acknowledged' && log.acknowledgedDate) {
          events.push({
            icon: <PackageCheck className="h-5 w-5" />,
            title: 'استلام في المخزن',
            description: `تم استلام الجهاز في مخزن '${log.warehouseName || 'غير معروف'}'.`,
            date: log.acknowledgedDate,
            color: 'bg-purple-500/20 text-purple-400',
            user: log.acknowledgedBy,
          });
        }
      }
    });

    // 4. Warehouse Transfer Events
    warehouseTransfers.forEach((transfer) => {
      if (Array.isArray(transfer.items) && transfer.items.some((item) => item.deviceId === searchedImei)) {
        const statusText = transfer.status === 'completed' ? 'مكتمل' : 'معلق';
        events.push({
          icon: <Shuffle className="h-5 w-5" />,
          title: `تحويل مخزني (${statusText})`,
          description: `تم نقل الجهاز من '${transfer.fromWarehouseName}' إلى '${transfer.toWarehouseName}'. أمر التحويل: ${transfer.transferNumber}.`,
          date: transfer.date,
          color: 'bg-gray-500/20 text-gray-400',
          user: transfer.employeeName,
        });
      }
    });

    // 5. Sale Event
    const sale = sales.find((s) =>
      (Array.isArray(s.items) && s.items.some((item) => item.deviceId === searchedImei))
    );
    if (sale) {
      events.push({
        icon: <ShoppingCart className="h-5 w-5" />,
        title: 'بيع',
        description: `تم بيع الجهاز للعميل '${sale.clientName}' ضمن فاتورة ${sale.soNumber}.`,
        date: sale.date,
        color: 'bg-green-500/20 text-green-400',
        user: 'قسم المبيعات', // Placeholder user
      });
    }

    // 6. Return and Replacement Events
    returns.forEach((returnOrder) => {
      const returnedItem = (Array.isArray(returnOrder.items) ? returnOrder.items.find(
        (item) => item.deviceId === searchedImei
      ) : null);
      const replacementItem = (Array.isArray(returnOrder.items) ? returnOrder.items.find(
        (item) => item.replacementDeviceId === searchedImei
      ) : null);

      if (returnedItem) {
        events.push({
          icon: <Undo2 className="h-5 w-5" />,
          title: 'إرجاع جهاز',
          description: `تم إرجاع هذا الجهاز من العميل '${returnOrder.clientName}' في أمر المرتجع رقم ${returnOrder.roNumber}. سبب الإرجاع: ${returnedItem.returnReason}.`,
          date: returnOrder.date,
          color: 'bg-red-500/20 text-red-400',
          user: 'قسم المرتجعات',
        });
      }

      if (replacementItem) {
        events.push({
          icon: <Replace className="h-5 w-5" />,
          title: 'جهاز بديل',
          description: `تم صرف هذا الجهاز كبديل للعميل '${returnOrder.clientName}' في أمر المرتجع رقم ${returnOrder.roNumber} عن الجهاز الأصلي (${replacementItem.deviceId}).`,
          date: returnOrder.date, // The replacement happens on the same return date
          color: 'bg-blue-500/20 text-blue-400',
          user: 'قسم المرتجعات',
        });
      }
    });

    return events
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .map((event) => ({
        ...event,
        formattedDate: formatArabicDate(event.date),
      }));
  }, [
    searchedImei,
    devices,
    sales,
    returns,
    supplyOrders,
    suppliers,
    evaluationOrders,
    maintenanceHistory,
    warehouseTransfers,
  ]);

  const customerViewDetails = useMemo(() => {
    if (!searchedImei) return null;

    const device = devices.find((d) => d.id === searchedImei);
    if (!device) return null;

    const lastSale = sales
      .filter((s) => (Array.isArray(s.items) && s.items.some((item) => item.deviceId === searchedImei))
      .sort(
        (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
      )[0];

    let warrantyInfo = {
      status: 'لا يوجد بيع مسجل',
      expiryDate: null,
      remaining: null,
    };

    if (lastSale) {
      const saleDate = new Date(lastSale.date);
      let expiryDate: Date | null = null;

      switch (lastSale.warrantyPeriod) {
        case '3d':
          expiryDate = addDays(saleDate, 3);
          break;
        case '1w':
          expiryDate = addDays(saleDate, 7);
          break;
        case '1m':
          expiryDate = addMonths(saleDate, 1);
          break;
        case '3m':
          expiryDate = addMonths(saleDate, 3);
          break;
        case '6m':
          expiryDate = addMonths(saleDate, 6);
          break;
        case '1y':
          expiryDate = addYears(saleDate, 1);
          break;
        default:
          break;
      }

      if (expiryDate) {
        const today = new Date();
        if (isAfter(expiryDate, today)) {
          warrantyInfo.status = 'في الضمان';
          warrantyInfo.remaining = formatDistanceToNowStrict(expiryDate,) {
            locale: ar,
            addSuffix: true,
          });
        } else {
          warrantyInfo.status = 'ضمان منتهي';
        }
        warrantyInfo.expiryDate = format(expiryDate, 'yyyy-MM-dd');
      } else {
        warrantyInfo.status = 'بدون ضمان';
      }
    }

    const replacementInfo = returns.find((r) =>
      (Array.isArray(r.items) && r.items.some((item) => item.replacementDeviceId === searchedImei)
    );
    const originalItem = replacementInfo?.items.find(
      (item) => item.replacementDeviceId === searchedImei
    );

    return {
      device,
      lastSale,
      warrantyInfo,
      originalItemInfo: originalItem
        ? {
            ...originalItem,
            returnDate: replacementInfo.date,
          }
        : null,
    };
  }, [searchedImei, devices, sales, returns]);

  const device = devices.find((d) => d.id === searchedImei);

  const handlePrint = async (action: 'print' | 'download') => {
    if (!searchedImei || !device) return;

    // تحضير البيانات للطباعة
    const deviceData = {
      model: device.model,
      id: searchedImei,
      status: device.status,
      lastSale: isCustomerView && customerViewDetails?.lastSale ? {
        clientName: customerViewDetails.lastSale.clientName,
        soNumber: customerViewDetails.lastSale.soNumber,
        opNumber: customerViewDetails.lastSale.opNumber,
        date: customerViewDetails.lastSale.date
      } : undefined,
      warrantyInfo: isCustomerView && customerViewDetails?.warrantyInfo ? {
        status: customerViewDetails.warrantyInfo.status,
        expiryDate: customerViewDetails.warrantyInfo.expiryDate,
        remaining: customerViewDetails.warrantyInfo.remaining
      } : undefined
    };

    try {
      // استخدام الدالة المحدثة للطباعة
      await printDeviceTrackingReport(deviceData, fullTimelineEvents,) {
        language: 'both',
        isCustomerView,
        action,
        filename: `${isCustomerView ? 'customer_' : ''}device_report_${searchedImei}.pdf`
      });
    } catch (error) {
      console.error('Error printing device report:', error);

      // العودة للطريقة القديمة في حالة الخطأ
      if (action === 'print') {
        const elementId = isCustomerView ? 'customer-view-container' : 'timeline-container';
        const title = isCustomerView
          ? `تقرير تتبع الجهاز (نسخة العميل)`
          : `سجل تاريخ الجهاز - ${device.model} (${searchedImei})`;

        printElementWithSettings(elementId, title,) {
          language: 'both',
          isCustomerView,
          action
        });
      }
    }

  };

  return (
    <div className="flex flex-col gap-6" dir="rtl">
      <Card className="search-card border-0 shadow-lg bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20">
        <CardHeader className="text-center pb-4">
          <CardTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            🔍 تتبع الاجهزة
          </CardTitle>
          <CardDescription className="text-lg text-gray-600 dark:text-gray-300">
            أدخل الرقم التسلسلي (IMEI) لعرض سجل تاريخ الجهاز المفصل
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="search-container">
            <div className="flex w-full max-w-2xl mx-auto items-center space-x-3 space-x-reverse">
              <div className="relative flex-1">
                <Input
                  type="text"
                  placeholder="أدخل IMEI أو امسح الباركود..."
                  className="text-right pl-12 pr-4 py-3 text-lg border-2 border-blue-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 rounded-xl shadow-sm transition-all duration-300 hover:shadow-md"
                  value={imei}
                  onChange={(e) => setImei(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
                <Barcode className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
              <Button
                onClick={handleSearch}
                className="px-8 py-3 text-lg font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
                disabled={!imei.trim()}
              >
                <Barcode className="ml-2 h-5 w-5" />
                بحث
              </Button>
            </div>
          </div>

          <div className="flex justify-center">
            <div className="flex items-center space-x-3 space-x-reverse bg-white dark:bg-gray-800 px-4 py-2 rounded-full shadow-md border border-gray-200 dark:border-gray-700">
              <Checkbox
                id="customer-view"
                checked={isCustomerView}
                onCheckedChange={(checked) => setIsCustomerView(!!checked)}
                className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
              />
              <Label htmlFor="customer-view" className="cursor-pointer font-medium text-gray-700 dark:text-gray-300">
                <User className="inline h-4 w-4 ml-1" />
                نسخة العميل
              </Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {searchedImei && !device && (
        <Card>
          <CardHeader>
            <CardTitle>نتيجة البحث</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-destructive">الجهاز غير موجود في النظام.</p>
          </CardContent>
        </Card>
      )}

      {searchedImei &&
        device &&
        (isCustomerView ? (
          customerViewDetails && (
            <div className="space-y-4">
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => setShowReportPreview(true)}>
                  <FileText className="ml-2 h-4 w-4" /> معاينة وطباعة التقرير
                </Button>
                <Button variant="outline" onClick={() => handlePrint('print')}>
                  <Printer className="ml-2 h-4 w-4" /> طباعة سريعة
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handlePrint('download')}
                >
                  <FileDown className="ml-2 h-4 w-4" /> تصدير PDF
                </Button>
              </div>
              <div id="customer-view-container" className="customer-view-enhanced grid grid-cols-1 md:grid-cols-2 gap-6 animate-in fade-in-50 print-section">
                <Card className="enhanced-card card-sale md:col-span-2">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <FileText className="h-6 w-6 text-green-600" />
                      </div>
                      <div>
                        <CardTitle className="text-xl font-bold text-gray-800">تفاصيل البيع</CardTitle>
                        <CardDescription className="text-gray-600 rtl-container">
                          آخر عملية بيع مسجلة لهذا الجهاز
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {customerViewDetails.lastSale ? (
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="bg-gray-50 p-4 rounded-lg border-r-4 border-green-500">
                          <div className="flex items-center gap-2 mb-2">
                            <User className="h-4 w-4 text-green-600" />
                            <span className="font-semibold text-gray-700">العميل</span>
                          </div>
                          <p className="text-gray-800 font-medium">
                            {customerViewDetails.lastSale.clientName}
                          </p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg border-r-4 border-blue-500">
                          <div className="flex items-center gap-2 mb-2">
                            <FileText className="h-4 w-4 text-blue-600" />
                            <span className="font-semibold text-gray-700">فاتورة البيع</span>
                          </div>
                          <p className="text-gray-800 font-medium">
                            {customerViewDetails.lastSale.soNumber}
                          </p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg border-r-4 border-purple-500">
                          <div className="flex items-center gap-2 mb-2">
                            <FileText className="h-4 w-4 text-purple-600" />
                            <span className="font-semibold text-gray-700">الفاتورة الرسمية</span>
                          </div>
                          <p className="text-gray-800 font-medium">
                            {customerViewDetails.lastSale.opNumber || 'لا يوجد'}
                          </p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg border-r-4 border-orange-500">
                          <div className="flex items-center gap-2 mb-2">
                            <ShoppingCart className="h-4 w-4 text-orange-600" />
                            <span className="font-semibold text-gray-700">تاريخ البيع</span>
                          </div>
                          <p className="text-gray-800 font-medium">
                            {formatArabicDate(customerViewDetails.lastSale.date)}
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <ShoppingCart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500 text-lg">
                          لا توجد عملية بيع مسجلة لهذا الجهاز
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
                <Card className={`enhanced-card card-warranty ${
                  customerViewDetails.warrantyInfo.status === 'في الضمان' ? 'warranty-active' :
                  customerViewDetails.warrantyInfo.status === 'ضمان منتهي' ? 'warranty-expired' : 'warranty-none'
                }`}>
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${
                        customerViewDetails.warrantyInfo.status === 'في الضمان' ? 'bg-green-100' :
                        customerViewDetails.warrantyInfo.status === 'ضمان منتهي' ? 'bg-red-100' : 'bg-gray-100'
                      }`}>
                        <ShieldCheck className={`h-6 w-6 ${
                          customerViewDetails.warrantyInfo.status === 'في الضمان' ? 'text-green-600' :
                          customerViewDetails.warrantyInfo.status === 'ضمان منتهي' ? 'text-red-600' : 'text-gray-600'
                        }`} />
                      </div>
                      <div>
                        <CardTitle className="text-xl font-bold text-gray-800">حالة الضمان</CardTitle>
                        <CardDescription className="text-gray-600">
                          معلومات الضمان الحالية للجهاز
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className={`p-4 rounded-lg text-center ${
                      customerViewDetails.warrantyInfo.status === 'في الضمان' ? 'bg-green-50 border-2 border-green-200' :
                      customerViewDetails.warrantyInfo.status === 'ضمان منتهي' ? 'bg-red-50 border-2 border-red-200' : 'bg-gray-50 border-2 border-gray-200'
                    }`}>
                      <div className={`text-2xl font-bold mb-2 ${
                        customerViewDetails.warrantyInfo.status === 'في الضمان' ? 'text-green-700' :
                        customerViewDetails.warrantyInfo.status === 'ضمان منتهي' ? 'text-red-700' : 'text-gray-700'
                      }`}>
                        {customerViewDetails.warrantyInfo.status}
                      </div>
                      {customerViewDetails.warrantyInfo.remaining && (
                        <div className="text-sm text-gray-600">
                          <strong>الوقت المتبقي:</strong> {customerViewDetails.warrantyInfo.remaining}
                        </div>
                      )}
                    </div>
                    {customerViewDetails.warrantyInfo.expiryDate && (
                      <div className="bg-gray-50 p-3 rounded-lg border-r-4 border-blue-500">
                        <div className="flex items-center gap-2 mb-1">
                          <ShieldCheck className="h-4 w-4 text-blue-600" />
                          <span className="font-semibold text-gray-700">تاريخ انتهاء الضمان</span>
                        </div>
                        <p className="text-gray-800 font-medium">
                          {customerViewDetails.warrantyInfo.expiryDate}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
                {customerViewDetails.originalItemInfo && (
                  <Card className="enhanced-card card-replacement md:col-span-2">
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-purple-100 rounded-lg">
                          <Replace className="h-6 w-6 text-purple-600" />
                        </div>
                        <div>
                          <CardTitle className="text-xl font-bold text-gray-800">معلومات الاستبدال</CardTitle>
                          <CardDescription className="text-gray-600">
                            تفاصيل عملية استبدال الجهاز
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="bg-purple-50 p-4 rounded-lg border-2 border-purple-200 text-center">
                        <Replace className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                        <p className="text-purple-800 font-medium text-lg">
                          هذا الجهاز تم تسليمه كبديل لجهاز آخر
                        </p>
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div className="bg-gray-50 p-4 rounded-lg border-r-4 border-purple-500">
                          <div className="flex items-center gap-2 mb-2">
                            <Package className="h-4 w-4 text-purple-600" />
                            <span className="font-semibold text-gray-700">الجهاز الأصلي</span>
                          </div>
                          <p className="text-gray-800 font-medium">
                            {customerViewDetails.originalItemInfo.model}
                          </p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg border-r-4 border-indigo-500">
                          <div className="flex items-center gap-2 mb-2">
                            <Barcode className="h-4 w-4 text-indigo-600" />
                            <span className="font-semibold text-gray-700">الرقم التسلسلي الأصلي</span>
                          </div>
                          <p className="text-gray-800 font-medium text-sm">
                            {customerViewDetails.originalItemInfo.deviceId}
                          </p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg border-r-4 border-orange-500">
                          <div className="flex items-center gap-2 mb-2">
                            <Undo2 className="h-4 w-4 text-orange-600" />
                            <span className="font-semibold text-gray-700">تاريخ الإرجاع</span>
                          </div>
                          <p className="text-gray-800 font-medium">
                            {formatArabicDate(customerViewDetails.originalItemInfo.returnDate)}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          )
        ) : (
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>سجل تاريخ الجهاز: {searchedImei}</CardTitle>
                  <CardDescription>
                    {device.model} - الحالة الحالية:{' '}
                    <span className="font-semibold">{device.status}</span>
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowReportPreview(true)}
                    title="معاينة وطباعة التقرير"
                  >
                    <FileText className="h-4 w-4 ml-1" />
                    معاينة التقرير
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handlePrint('print')}
                    title="طباعة سريعة"
                  >
                    <Printer className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handlePrint('download')}
                    title="تصدير PDF"
                  >
                    <FileDown className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {fullTimelineEvents.length > 0 ? (
                <div id="timeline-container" className="enhanced-timeline print-section">
                  {fullTimelineEvents.map((event, index) => {
                    // تحديد نوع الحدث للتلوين
                    const eventType = event.title.includes('توريد') ? 'supply' :
                                    event.title.includes('فحص') ? 'evaluation' :
                                    event.title.includes('صيانة') ? 'maintenance' :
                                    event.title.includes('تحويل') ? 'transfer' :
                                    event.title.includes('بيع') ? 'sale' :
                                    event.title.includes('إرجاع') ? 'return' :
                                    event.title.includes('بديل') ? 'replacement' : 'default';

                    return (
                      <div
                        key={index}
                        className={`enhanced-timeline-item timeline-${eventType} timeline-event`}
                      >
                        <div className="flex items-start gap-4">
                          <div className={`flex h-12 w-12 items-center justify-center rounded-full ${event.color} shadow-lg icon-bounce`}>
                            {event.icon}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
                              <h4 className="font-bold text-lg timeline-title text-gray-800">
                                {event.title}
                              </h4>
                              <time className="text-sm font-medium text-blue-600 bg-blue-50 px-3 py-1 rounded-full timeline-date">
                                {event.formattedDate}
                              </time>
                            </div>
                            <p className="text-gray-600 leading-relaxed timeline-description mb-3">
                              {event.description}
                            </p>
                            {event.user && (
                              <div className="flex items-center gap-2 text-sm text-gray-500 timeline-user">
                                <div className="flex items-center gap-1 bg-gray-100 px-2 py-1 rounded-full">
                                  <User className="h-3 w-3" />
                                  <span>بواسطة: {event.user}</span>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <p className="text-muted-foreground">
                  لا يوجد سجل تاريخ مسجل لهذا الجهاز.
                </p>
              )}
            </CardContent>
          </Card>
        ))}

      {/* مكون معاينة التقرير */}
      {searchedImei && device && (
        <ReportPreview
          isOpen={showReportPreview}
          onClose={() => setShowReportPreview(false)}
          deviceData={{
            model: device.model,
            id: searchedImei,
            status: device.status,
            lastSale: isCustomerView && customerViewDetails?.lastSale ? {
              clientName: customerViewDetails.lastSale.clientName,
              soNumber: customerViewDetails.lastSale.soNumber,
              opNumber: customerViewDetails.lastSale.opNumber,
              date: customerViewDetails.lastSale.date
            } : undefined,
            warrantyInfo: isCustomerView && customerViewDetails?.warrantyInfo ? {
              status: customerViewDetails.warrantyInfo.status,
              expiryDate: customerViewDetails.warrantyInfo.expiryDate,
              remaining: customerViewDetails.warrantyInfo.remaining
            } : undefined
          }}
          timelineEvents={fullTimelineEvents.map(event => ({
            id: `${event.date}-${event.title}`,
            type: event.title.includes('بيع') ? 'بيع' :
                  event.title.includes('إرجاع') ? 'إرجاع' :
                  event.title.includes('صيانة') ? 'صيانة' :
                  event.title.includes('تقييم') ? 'تقييم' :
                  event.title.includes('توريد') ? 'توريد' : 'عام',
            title: event.title,
            description: event.description,
            date: event.date,
            user: event.user
          }))}
        />
      )}
    </div>
  );
}